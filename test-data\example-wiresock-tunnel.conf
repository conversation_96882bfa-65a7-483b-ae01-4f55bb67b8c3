[Interface]
PrivateKey = interface-private-key
Address = ********, 662a:622d:33aa:6ad0:adba:865f:d13c:85f1
DNS = *******, *******, 662a:622d:33aa:6ad0:adba:865f:d13c:85f1
Port = 53704
MTU = 1400

[Peer]
PublicKey = peer-public-key
PresharedKey = peer-preshared-key
Endpoint = endpoint-ip:53703
PersistentKeepalive = 20

# WireSock parameters
AllowedIPs = 0.0.0.0/0, ::/0, 662a:622d:33aa:6ad0:adba:865f:d13c:85f1
AllowedApps = appa, A:\Program Files\Test, appb,B:\Test\, c:\Test
DisallowedIPs = *******, ***********/24, ::/0, 662a:622d:33aa:6ad0:adba:865f:d13c:85f1
DisallowedApps = appa, E:\Program Files\Test, appb,F:\Test\, g:\Test