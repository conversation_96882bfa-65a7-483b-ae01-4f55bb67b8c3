import type WiresockStateModel from '../../models/WiresockStateModel.ts'
import { useEffect, useState } from 'react'
import TunnelManager from '../../models/TunnelManager.ts'
import { ArrowRightStartOnRectangleIcon } from '@heroicons/react/16/solid'
import mqtt from 'mqtt'

interface SidebarProps {
  tunnelManager: TunnelManager | null
  selectedTunnelID: string | null
  wiresockState: WiresockStateModel | null
  setSelectedTunnelID: (tunnelID: string) => void
  setTunnelManager: any
  setToken: any
  enableTunnel: (tunnelID: any) => void
  disableTunnel: () => Promise<void>
  switchValue: any
}

function Sidebar({
  setToken,
  switchValue,
  tunnelManager,
  setTunnelManager,
  selectedTunnelID,
  wiresockState,
  setSelectedTunnelID,
  enableTunnel,
  disableTunnel,
}: SidebarProps): JSX.Element {
  const [menuItems, setMenuItems] = useState<any[]>([])

  function getList() {
    const token = localStorage.getItem('token')
    console.log(token)
    if (token == null) {
      console.error('Token not found in localStorage')
      return
    }
    fetch('https://cloud.sdw.86.ltd/srv/v1/me/wan', {
      method: 'get', // 指定请求方法为 POST
      headers: {
        'Content-Type': 'application/json', // 设置头部内容类型为 JSON
        Authorization: token,
      },
    })
      .then(async (response) => await response.json())
      .then((data) => {
        console.log(data, 'data', token)
        setMenuItems(data.result.rows)
        if (data.result.rows.length > 0 && client == null) {
          mqttConnect()
        }
      })
  }

  useEffect(() => {
    getList()
    setTimeout(() => {
      refreshList()
    }, 1500)
  }, [])
  useEffect(() => {
    refreshList()
  }, [selectedTunnelID, switchValue]);

  function saveTunnel(info: any, wan: any, flag?: boolean): void {
    console.log(info, wan, flag, 9999)
    //  获取数组中的type为1的所有对象组成新数组
    const peers = wan.peer.filter((item: any) => item.type === 1)
    const allowedIps = peers.map((p: any) => {
      const [, , c, d] = p.address.split('.')
      return `10.${c}.${d}.0/24`
    })
    allowedIps.push(wan.ipNet)
    if(wan.type==1){
      const [, , c, d] = wan.innerIp.split('.')
      let str =  `10.${c}.${d}.0/24`
      allowedIps.push(str)
    }
    console.log(peers, 'peers', allowedIps)
    const editedTunnel: any = {
      id: info.id,
      name: info.name,
      interface: {
        ipv4Address: info.address + '/32',
        ipv6Address: '',
        port: '',
        privateKey: info.privateKey,
        dns: '',
        mtu: '1420',
      },
      peer: {
        endpoint: wan.remoteIp,
        port: wan.port + '',
        publicKey: wan.publicKey,
        presharedKey: '',
        persistentKeepalive: '25',
      },
      rules: {
        allowed: {
          apps: '',
          folders: '',
          ipAddresses: allowedIps.join(','),
        },
        disallowed: {
          apps: '',
          folders: '',
          ipAddresses: '',
        },
      },
    }
    console.log(editedTunnel, 'editedTunnel')
    if (tunnelManager?.tunnels[info.id] != null) {
      tunnelManager.tunnels[info.id] = editedTunnel
      // Update the tunnelManager state with the updated tunnels
      setTunnelManager(new TunnelManager(tunnelManager.tunnels))
    } else {
      // Create a new object for the tunnels with the added/updated tunnel
      const updatedTunnels = {
        ...tunnelManager?.tunnels,
        [info.id]: editedTunnel,
      }
      // Update the tunnelManager state with the updated tunnels
      setTunnelManager(new TunnelManager(updatedTunnels))
    }
    // Update the selected tunnel ID
    setSelectedTunnelID(info.id as string)
  }

  function exitWindows(): void {
    localStorage.removeItem('token')
    if (client != null) {
      client.end()
    }
    setToken('')
  }
  // mqtt
  /***
   * 浏览器环境
   * 使用协议为 ws 和 wss 的 MQTT over WebSocket 连接
   * EMQX 的 ws 连接默认端口为 8083，wss 为 8084
   * 注意需要在连接地址后加上一个 path, 例如 /mqtt
   */
  const url = 'wss://mqtt.sdw.86.ltd/mqtt'
  //  连接的话需要先断开
  // 创建客户端实例
  const options = {
    clean: true,
    reconnectPeriod: 100000,
    connectTimeout: 4000, // 认证信息
    clientId: 'windows-' + Math.random().toString(36).substring(7),
  }
  const [client, setClient] = useState<any>(null)
  const mqttConnect = () => {
    console.log('mqttConnect', '我又来创建了')
    const token = localStorage.getItem('token')
    if (token != null) {
      console.log('mqttConnect', '我又来创建了111', client,options)
      if (client == null) {
        const clientNew = mqtt.connect(url, options)
        setClient(clientNew)
        clientNew.on('connect', function () {
          console.log('Connected')
          const userId = localStorage.getItem('userId')
          clientNew.subscribe(`sdw/user/${userId}/evt/cmd`, function (err) {
            console.log('Subscribed', err)
          })
        })
        // 接收消息
        clientNew.on('message', function (topic: any, message) {
          console.log('message', topic, message.toString())
          // message is Buffer
          const str = message.toString()
          const info = JSON.parse(str)
          dealData(info)
        })
      }
    }
  }
  const dealData = (info: any) => {
    console.log(info)
    refreshList()
  }

  function refreshList(): void {
    const token = localStorage.getItem('token')
    if (token == null) {
      return
    }
    fetch('https://cloud.sdw.86.ltd/srv/v1/me/wan', {
      method: 'get', // 指定请求方法为 POST
      headers: {
        'Content-Type': 'application/json', // 设置头部内容类型为 JSON
        Authorization: token,
      },
    })
      .then(async (response) => await response.json())
      .then(async (data: any) => {
        console.log(data, 'data', selectedTunnelID)
        if (switchValue?.tunnel_status === 'CONNECTED' && switchValue.tunnel_id === selectedTunnelID) {
          await disableTunnel()
        }
        const arr = data.result.rows
        let info = null
        let wan = null
        for (const i in arr) {
          const list = arr[i].peer
          for (const n in list) {
            if (list[n].type === 2) {
              saveTunnel(list[n], arr[i], true)
            }
            if (list[n].id === selectedTunnelID) {
              info = list[n]
              wan = arr[i]
            }
          }
        }
        if (info != null && wan != null) {
          saveTunnel(info, wan)
        }
        if (switchValue?.tunnel_status === 'CONNECTED' && switchValue.tunnel_id === selectedTunnelID) {
          enableTunnel(selectedTunnelID)
        }
        console.log(selectedTunnelID, switchValue, 888888)
        setMenuItems(data.result.rows)
      })
  }

  return (
    <div className="fixed inset-y-0 z-50 flex w-72 flex-col">
      <div className="flex grow flex-col gap-y-5 overflow-y-auto bg-gray-900 px-6">
        <h1 className="text-4xl font-semibold text-white pt-8">sdw</h1>
        <nav className="flex flex-1 flex-col">
          <ul role="list" className="flex flex-1 flex-col gap-y-4">
            <li>
              <ul role="list" className="-mx-2 space-y-1 cursor-pointer">
                {menuItems.map((item) => (
                  <li key={item.id}>
                    {item.peer.map((info: any) => (
                      <div key={info.id}>
                        {info.type === 2 ? (
                          <div
                            key={info.id}
                            className={`${selectedTunnelID === info.id ? 'bg-gray-800 text-white' : 'text-gray-400 hover:text-white hover:bg-gray-800'} group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold`}
                            onClick={() => {
                              saveTunnel(info, item)
                            }}
                          >
                            <span
                              style={{
                                background: 'green',
                                color: '#fff',
                                padding: '0 10px',
                                borderRadius: '10px',
                              }}
                            >
                              {item.customName}
                            </span>
                            <span className="truncate">{info?.name ?? 'Undefined'}</span>
                            {wiresockState?.tunnel_status === 'CONNECTED' && wiresockState.tunnel_id === info.id ? (
                              <span
                                className="ml-auto bg-green-400/30 text-green-400 rounded-full p-1 my-auto"
                                aria-hidden="true"
                              >
                                <div className="h-2 w-2 rounded-full bg-current" />
                              </span>
                            ) : null}
                          </div>
                        ) : null}
                      </div>
                    ))}
                  </li>
                ))}
              </ul>
            </li>
          </ul>
          <button
            onClick={() => {
              exitWindows()
            }}
            type="button"
            className="w-full text-gray-400 hover:text-white hover:bg-gray-800 flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold mb-4"
          >
            <span className="flex h-6 w-6 shrink-0 items-center justify-center rounded-lg border  border-gray-700 bg-gray-800">
              <ArrowRightStartOnRectangleIcon className="h-4 w-4 text-purple-400" aria-hidden="true" />
            </span>
            <span className="truncate">退出登录</span>
          </button>
        </nav>
      </div>
    </div>
  )
}

export default Sidebar
