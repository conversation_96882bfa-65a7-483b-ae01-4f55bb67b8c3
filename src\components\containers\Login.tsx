import { useState } from 'react'

interface SetupProps {
  setToken: (token: any) => void
}
function Login({ setToken}: SetupProps): JSX.Element {
  function handleInputChange(event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>): void {
    const { name, value } = event.target
    const keys = name.split('.')
    if (keys[0] == 'phone') {
      setPhone(value)
    }
    if (keys[0] == 'code') {
      setCode(value)
    }
  }

  function handleSaveButtonClick(): void {
    fetch('https://cloud.sdw.86.ltd/srv/auth/tel/sinInOrUp', {
      method: 'POST', // 指定请求方法为 POST
      headers: {
        'Content-Type': 'application/json', // 设置头部内容类型为 JSON
      },
      body: JSON.stringify({
        // 将请求体内容转换为字符串
        tel: phone,
        code: code,
      }),
    })
      .then((response) => response.json())
      .then((data) => {
        console.log(data)
        localStorage.setItem('token', data.result.token)
        localStorage.setItem('userId', data.result.id)
        setToken(data.result.token)
      })
  }
  const [codeStatus, setCodeStatus] = useState(false)
  const [codeStr, setCodeStr] = useState('获取验证码')
  let timer:any
  function handleSaveButtonCode (): void {
    if (codeStatus) {
      return
    } else {
      setCodeStatus(true)
    }
    fetch('https://cloud.sdw.86.ltd/srv/auth/tel/code', {
      method: 'POST', // 指定请求方法为 POST
      headers: {
        'Content-Type': 'application/json', // 设置头部内容类型为 JSON
      },
      body: JSON.stringify({
        // 将请求体内容转换为字符串
        tel: phone,
      }),
    })
      .then((response) => response.json())
      .then(() => {
        setCodeStr('60s')
        let time = 60
        timer = setInterval(() => {
          time--
          setCodeStr( `${time}s`)
          if (time === 0) {
            clearInterval(timer)
            timer = null
            setCodeStr('重新获取')
            setCodeStatus(false)
          }
        }, 1000)
      })
  }
  //  短信验证码


  const [phone, setPhone] = useState('')
  const [code, setCode] = useState('')
  return (
    <div className="w-full">
      <div className=" flex justify-center	items-center	flex-col	" style={{ height: '100vh' }}>
        <h1 className="text-2xl font-semibold text-black pt-8 mb-4">SDWAN 登录</h1>
        <div className="flex justify-center	items-center	">
          <div className="mt-2 items-center	flex">
            <label htmlFor="phone" className="block text-sm font-medium leading-6 text-gray-900 mr-2 w-20">
              账号
            </label>
            <div className="w-80">
              <input
                defaultValue={phone}
                onChange={handleInputChange}
                name="phone"
                spellCheck="false"
                className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 text-sm leading-6"
              />
            </div>
          </div>
        </div>
        <div className="flex justify-center	items-center	">
          <div className="mt-2 items-center	flex">
            <label htmlFor="code" className="block text-sm font-medium leading-6 text-gray-900 mr-2 w-20">
              验证码
            </label>
            <div className="w-80 flex justify-center	items-center">
              <input
                defaultValue={code}
                onChange={handleInputChange}
                name="code"
                spellCheck="false"
                className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 text-sm leading-6"
              />
              <button
                onClick={handleSaveButtonCode}
                className="w-40 rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 ml-2"
              >
                {codeStr}
              </button>
            </div>
          </div>
        </div>
        <div className="mt-6 flex align-center items-center">
          <button
            onClick={handleSaveButtonClick}
            className="w-60 rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
          >
            登录
          </button>
        </div>
      </div>
    </div>
  )
}

export default Login
