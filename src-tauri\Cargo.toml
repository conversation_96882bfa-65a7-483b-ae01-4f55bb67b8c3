[package]
name = "sdw"
version = "1.0.14"
authors = ["SDW"]
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[build-dependencies]
tauri-build = { version = "1.5.2", features = [] }

[dependencies]
tauri = { version = "1.6.6", features = ["api-all", "devtools", "system-tray", "icon-png", "icon-ico"] }
serde = { version = "1.0.202", features = ["derive"] }
serde_json = "1.0.117"
home = "0.5.9"
winreg = "0.52.0"
sysinfo = "0.30.12"
once_cell = "1.19.0"
lazy_static = "1.4.0"
tauri-plugin-window-state = { git = "https://github.com/tauri-apps/plugins-workspace", branch = "v1" }
tauri-plugin-single-instance = { git = "https://github.com/tauri-apps/plugins-workspace", branch = "v1" }
tauri-plugin-autostart = { git = "https://github.com/tauri-apps/plugins-workspace", branch = "v1" }

[dependencies.windows]
version = "0.43.0"
features = [
    "Win32_Foundation",
    "Win32_Security",
    "Win32_System_Threading",
    "Win32_System_JobObjects",
]

[features]
# this feature is used for production builds or when `devPath` points to the filesystem
# DO NOT REMOVE!!
custom-protocol = ["tauri/custom-protocol"]
