{"build": {"beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build", "devPath": "http://localhost:1420", "distDir": "../dist", "withGlobalTauri": false}, "package": {"productName": "sdw", "version": "1.0.14"}, "tauri": {"systemTray": {"iconPath": "icons/icon.png", "iconAsTemplate": true}, "allowlist": {"fs": {"scope": ["$RESOURCE/*"]}, "all": true, "shell": {"all": false, "open": true}}, "bundle": {"active": true, "category": "DeveloperTool", "copyright": "", "targets": "all", "identifier": "com.sdw.to", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "resources": ["wiresock/wiresock-vpn-client-x64-********.msi"], "windows": {"certificateThumbprint": null, "digestAlgorithm": "sha256", "timestampUrl": "", "webviewInstallMode": {"type": "embed<PERSON><PERSON><PERSON><PERSON>"}}}, "security": {"csp": "default-src 'self'; img-src * data:; connect-src 'self' https://cloud.sdw.86.ltd;"}, "windows": [{"fullscreen": false, "resizable": true, "title": "sdw", "width": 900, "height": 700, "visible": false}]}}