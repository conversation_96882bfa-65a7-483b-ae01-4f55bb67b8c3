{"name": "sdw", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri", "lint": "npx eslint \"./src/**/*.{ts,tsx}\""}, "dependencies": {"@headlessui/react": "^2.0.3", "@heroicons/react": "^2.1.3", "@tailwindcss/forms": "^0.5.7", "@tauri-apps/api": "^1.5.6", "mqtt": "^5.10.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.23.1", "tauri-plugin-autostart-api": "github:tauri-apps/tauri-plugin-autostart#v1", "tweetnacl": "^1.0.3", "tweetnacl-util": "^0.15.1"}, "devDependencies": {"@tauri-apps/cli": "^1.5.14", "@types/node": "^20.12.12", "@types/react": "^18.3.2", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^6.4.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.19", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-config-standard-with-typescript": "^43.0.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-n": "^16.6.2", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-react": "^7.34.1", "postcss": "^8.4.38", "prettier": "^3.2.5", "prettier-config-standard": "^7.0.0", "tailwindcss": "^3.4.3", "typescript": "^5.4.5", "vite": "^5.2.11"}}